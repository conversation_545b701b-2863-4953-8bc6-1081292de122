import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:gameflex_mobile/screens/video_splash_screen.dart';
import 'package:gameflex_mobile/screens/login_screen.dart';
import 'package:gameflex_mobile/screens/home_screen.dart';
import 'package:gameflex_mobile/providers/auth_provider.dart';

class SplashManager extends StatefulWidget {
  const SplashManager({super.key});

  @override
  State<SplashManager> createState() => _SplashManagerState();
}

class _SplashManagerState extends State<SplashManager> {
  bool _showVideoSplash = true;
  bool _authTimeout = false;

  void _onVideoComplete() {
    setState(() {
      _showVideoSplash = false;
    });

    // Start a timeout for auth loading
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          _authTimeout = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_showVideoSplash) {
      return VideoSplashScreen(
        onVideoComplete: _onVideoComplete,
      );
    }

    // After video completes, show the main app with auth logic
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // If auth is taking too long, force to login screen
        if (_authTimeout && (authProvider.status == AuthStatus.initial || authProvider.status == AuthStatus.loading)) {
          return const LoginScreen();
        }

        switch (authProvider.status) {
          case AuthStatus.initial:
          case AuthStatus.loading:
            return const Scaffold(
              backgroundColor: Colors.black, // Keep black background for smooth transition
              body: Center(
                child: CircularProgressIndicator(
                  color: Colors.white,
                ),
              ),
            );
          case AuthStatus.authenticated:
            return const HomeScreen();
          case AuthStatus.unauthenticated:
          case AuthStatus.error:
            return const LoginScreen();
        }
      },
    );
  }
}
