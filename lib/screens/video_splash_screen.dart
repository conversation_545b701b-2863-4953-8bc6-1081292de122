import 'dart:io';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

class VideoSplashScreen extends StatefulWidget {
  final VoidCallback onVideoComplete;

  const VideoSplashScreen({
    super.key,
    required this.onVideoComplete,
  });

  @override
  State<VideoSplashScreen> createState() => _VideoSplashScreenState();
}

class _VideoSplashScreenState extends State<VideoSplashScreen> {
  VideoPlayerController? _controller;
  bool _isVideoInitialized = false;
  bool _hasVideoCompleted = false;
  bool _isVideoSupported = false;

  @override
  void initState() {
    super.initState();
    _checkPlatformAndInitialize();
  }

  void _checkPlatformAndInitialize() {
    // Video player is supported on Android, iOS, and Web
    _isVideoSupported = Platform.isAndroid || Platform.isIOS;

    if (_isVideoSupported) {
      _initializeVideo();
    } else {
      // For desktop platforms, show static splash for a few seconds
      _showStaticSplash();
    }
  }

  Future<void> _initializeVideo() async {
    try {
      _controller = VideoPlayerController.asset('assets/videos/splash/swords.mp4');

      await _controller!.initialize();

      if (mounted) {
        setState(() {
          _isVideoInitialized = true;
        });

        // Start playing the video
        await _controller!.play();

        // Listen for video completion
        _controller!.addListener(_videoListener);
      }
    } catch (e) {
      // If video fails to load, skip to main app after a short delay
      debugPrint('Video splash screen error: $e');
      _showStaticSplash();
    }
  }

  void _showStaticSplash() {
    // Show static splash for 3 seconds on desktop platforms
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && !_hasVideoCompleted) {
        _onVideoComplete();
      }
    });
  }

  void _videoListener() {
    if (_controller != null &&
        _controller!.value.position >= _controller!.value.duration &&
        _controller!.value.duration > Duration.zero &&
        !_hasVideoCompleted) {
      _onVideoComplete();
    }
  }

  void _onVideoComplete() {
    if (_hasVideoCompleted) return;
    
    _hasVideoCompleted = true;
    widget.onVideoComplete();
  }

  @override
  void dispose() {
    if (_controller != null) {
      _controller!.removeListener(_videoListener);
      _controller!.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black, // Black background to blend with video
      body: Stack(
        children: [
          // Video player for supported platforms
          if (_isVideoSupported && _isVideoInitialized && _controller != null)
            Center(
              child: AspectRatio(
                aspectRatio: _controller!.value.aspectRatio,
                child: VideoPlayer(_controller!),
              ),
            ),

          // Static splash for desktop platforms or when video fails
          if (!_isVideoSupported || (!_isVideoInitialized && _isVideoSupported))
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // GameFlex logo
                  Image.asset(
                    'assets/images/logos/gameflex_logo.png',
                    width: 200,
                    height: 200,
                    fit: BoxFit.contain,
                  ),
                  const SizedBox(height: 32),
                  // Loading indicator
                  const CircularProgressIndicator(
                    color: Colors.white,
                  ),
                ],
              ),
            ),

          // Tap to skip (optional - you can remove this if you don't want skip functionality)
          Positioned.fill(
            child: GestureDetector(
              onTap: () {
                if (!_hasVideoCompleted) {
                  _onVideoComplete();
                }
              },
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),

          // Skip button (optional)
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            right: 16,
            child: TextButton(
              onPressed: () {
                if (!_hasVideoCompleted) {
                  _onVideoComplete();
                }
              },
              child: const Text(
                'Skip',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
