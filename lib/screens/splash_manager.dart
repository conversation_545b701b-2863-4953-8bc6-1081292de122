import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:gameflex_mobile/screens/video_splash_screen.dart';
import 'package:gameflex_mobile/screens/login_screen.dart';
import 'package:gameflex_mobile/screens/home_screen.dart';
import 'package:gameflex_mobile/providers/auth_provider.dart';

class SplashManager extends StatefulWidget {
  const SplashManager({super.key});

  @override
  State<SplashManager> createState() => _SplashManagerState();
}

class _SplashManagerState extends State<SplashManager> {
  bool _showVideoSplash = true;

  void _onVideoComplete() {
    setState(() {
      _showVideoSplash = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_showVideoSplash) {
      return VideoSplashScreen(
        onVideoComplete: _onVideoComplete,
      );
    }

    // After video completes, show the main app with auth logic
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        switch (authProvider.status) {
          case AuthStatus.initial:
          case AuthStatus.loading:
            return const Scaffold(
              backgroundColor: Colors.black, // Keep black background for smooth transition
              body: Center(
                child: CircularProgressIndicator(
                  color: Colors.white,
                ),
              ),
            );
          case AuthStatus.authenticated:
            return const HomeScreen();
          case AuthStatus.unauthenticated:
          case AuthStatus.error:
            return const LoginScreen();
        }
      },
    );
  }
}
