import 'package:flutter/material.dart';

class VideoSplashScreen extends StatefulWidget {
  final VoidCallback onVideoComplete;

  const VideoSplashScreen({
    super.key,
    required this.onVideoComplete,
  });

  @override
  State<VideoSplashScreen> createState() => _VideoSplashScreenState();
}

class _VideoSplashScreenState extends State<VideoSplashScreen>
    with TickerProviderStateMixin {
  bool _hasVideoCompleted = false;
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startSplashSequence();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
  }

  void _startSplashSequence() async {
    // Start animations
    _fadeController.forward();
    _scaleController.forward();

    // Show splash for 3 seconds (simulating video duration)
    await Future.delayed(const Duration(seconds: 3));

    if (mounted && !_hasVideoCompleted) {
      _onVideoComplete();
    }
  }

  void _onVideoComplete() {
    if (_hasVideoCompleted) return;

    _hasVideoCompleted = true;
    widget.onVideoComplete();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black, // Black background to blend with video
      body: Stack(
        children: [
          // Animated GameFlex logo splash
          Center(
            child: AnimatedBuilder(
              animation: _fadeController,
              builder: (context, child) {
                return FadeTransition(
                  opacity: _fadeAnimation,
                  child: AnimatedBuilder(
                    animation: _scaleController,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _scaleAnimation.value,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // GameFlex logo
                            Image.asset(
                              'assets/images/logos/gameflex_logo.png',
                              width: 200,
                              height: 200,
                              fit: BoxFit.contain,
                            ),
                            const SizedBox(height: 32),
                            // Pulsing loading indicator
                            TweenAnimationBuilder<double>(
                              tween: Tween(begin: 0.5, end: 1.0),
                              duration: const Duration(milliseconds: 800),
                              builder: (context, value, child) {
                                return Transform.scale(
                                  scale: value,
                                  child: const CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 3,
                                  ),
                                );
                              },
                              onEnd: () {
                                // Restart the pulsing animation
                                if (mounted) {
                                  setState(() {});
                                }
                              },
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          ),

          // Tap to skip (optional - you can remove this if you don't want skip functionality)
          Positioned.fill(
            child: GestureDetector(
              onTap: () {
                if (!_hasVideoCompleted) {
                  _onVideoComplete();
                }
              },
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),

          // Skip button (optional)
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            right: 16,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: TextButton(
                onPressed: () {
                  if (!_hasVideoCompleted) {
                    _onVideoComplete();
                  }
                },
                child: const Text(
                  'Skip',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
